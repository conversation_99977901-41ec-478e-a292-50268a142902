#!/usr/bin/env python3
"""
Test script to demonstrate image generation with attachments functionality.

This script shows how to use the enhanced ImageGenerationView that now supports
file attachments. When you attach images, the system will:

1. Process the attached images
2. Use a vision model to describe them
3. Enhance the original prompt with the image descriptions
4. Generate a new image based on the enhanced prompt

Usage:
    python test_image_generation_with_attachments.py

Note: This is a demonstration script. In a real application, you would:
- Upload files first using the file upload API
- Get the file IDs from the upload response
- Use those IDs in the attachments array
"""

import json

def create_test_payload_without_attachments():
    """Create a test payload for image generation without attachments (original functionality)"""
    return {
        "conversation_id": None,
        "model": "dall-e-3",
        "action": "generate",
        "message": {
            "author": {
                "role": "user"
            },
            "content": {
                "content_type": "text",
                "parts": ["A beautiful sunset over mountains"],
                "size": "1024x1024",
                "style": "natural"
            },
            "metadata": {
                "attachments": []
            }
        }
    }

def create_test_payload_with_attachments():
    """Create a test payload for image generation with attachments (new functionality)"""
    return {
        "conversation_id": None,
        "model": "dall-e-3", 
        "action": "generate",
        "message": {
            "author": {
                "role": "user"
            },
            "content": {
                "content_type": "text",
                "parts": ["Create a similar image but with different colors"],
                "size": "1024x1024", 
                "style": "vivid"
            },
            "metadata": {
                "attachments": [
                    {
                        "id": 123,  # This would be the ID of an uploaded image file
                        "file_type": "jpg",
                        "original_name": "reference_image.jpg"
                    }
                ]
            }
        }
    }

def main():
    print("=== Image Generation with Attachments Demo ===\n")
    
    print("1. Original functionality (without attachments):")
    payload1 = create_test_payload_without_attachments()
    print(json.dumps(payload1, indent=2))
    print("\nThis will generate an image based only on the text prompt.\n")
    
    print("2. New functionality (with attachments):")
    payload2 = create_test_payload_with_attachments()
    print(json.dumps(payload2, indent=2))
    print("\nThis will:")
    print("- Process the attached image (ID: 123)")
    print("- Use GPT-4o vision to describe the attached image")
    print("- Enhance the prompt: 'Create a similar image but with different colors'")
    print("- Generate a new image based on the enhanced prompt\n")
    
    print("=== How it works ===")
    print("1. Upload an image file using: POST /api/files/v1/upload/")
    print("2. Get the file ID from the upload response")
    print("3. Use that ID in the attachments array")
    print("4. Send the request to: POST /api/llm-manager/v1/images/generation/")
    print("5. The system will analyze your attached images and create a better prompt")
    print("6. DALL-E 3 will generate an image based on the enhanced prompt\n")
    
    print("=== API Endpoint ===")
    print("POST /api/llm-manager/v1/images/generation/")
    print("Content-Type: application/json")
    print("Authorization: Bearer <your-token>")

if __name__ == "__main__":
    main()
