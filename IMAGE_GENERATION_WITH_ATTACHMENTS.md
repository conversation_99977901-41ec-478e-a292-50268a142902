# Image Generation with Attachments

## Overview

The image generation feature now supports file attachments, allowing users to upload reference images and documents that will be analyzed and used to enhance the generation prompt. This creates more contextually aware and accurate image generation.

## How It Works

1. **Upload Reference Files**: Users can attach image files and documents to their image generation requests
2. **File Analysis**: The system uses GPT-4o to analyze and describe the attached files:
   - **Images**: Analyzes visual elements, style, composition, colors, and artistic techniques
   - **Documents**: Extracts visual concepts, themes, and descriptions relevant to image generation
3. **Prompt Enhancement**: The original text prompt is enhanced with detailed analysis of the attached files
4. **Image Generation**: DALL-E 3 generates a new image based on the enhanced prompt

## Supported File Types

### Image Files (Vision Analysis)
- PNG (`.png`)
- JPEG (`.jpg`, `.jpeg`)
- GIF (`.gif`)
- WebP (`.webp`)

### Document Files (Content Analysis)
- PDF (`.pdf`)
- Text files (`.txt`)
- CSV (`.csv`)
- JSON (`.json`)
- Markdown (`.md`)
- Word documents (`.docx`)
- Excel spreadsheets (`.xlsx`)
- PowerPoint presentations (`.pptx`)

## API Usage

### Endpoint
```
POST /api/llm-manager/v1/images/generation/
```

### Request Format

#### Without Attachments (Original)
```json
{
  "conversation_id": null,
  "model": "dall-e-3",
  "action": "generate",
  "message": {
    "author": {
      "role": "user"
    },
    "content": {
      "content_type": "text",
      "parts": ["A beautiful sunset over mountains"],
      "size": "1024x1024",
      "style": "natural"
    },
    "metadata": {
      "attachments": []
    }
  }
}
```

#### With Attachments (New Feature)
```json
{
  "conversation_id": null,
  "model": "dall-e-3",
  "action": "generate",
  "message": {
    "author": {
      "role": "user"
    },
    "content": {
      "content_type": "text",
      "parts": ["Create a similar image but with different colors"],
      "size": "1024x1024",
      "style": "vivid"
    },
    "metadata": {
      "attachments": [
        {
          "id": 123,
          "file_type": "jpg",
          "original_name": "reference_image.jpg"
        }
      ]
    }
  }
}
```

## Step-by-Step Usage

### 1. Upload Reference Images
First, upload your reference images using the file upload API:

```bash
curl -X POST \
  -H "Authorization: Bearer <your-token>" \
  -F "files[]=@reference_image.jpg" \
  http://your-domain/api/files/v1/upload/
```

Response:
```json
{
  "message": "Files uploaded successfully",
  "data": {
    "id": 123,
    "file_url": "https://your-domain/media/uploads/reference_image.jpg",
    "file_type": "jpg",
    "original_name": "reference_image.jpg",
    "size": 245760,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### 2. Generate Image with Attachments
Use the file ID from step 1 in your image generation request:

```bash
curl -X POST \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "message": {
      "author": {"role": "user"},
      "content": {
        "content_type": "text",
        "parts": ["Create a similar landscape but in autumn colors"],
        "size": "1024x1024",
        "style": "vivid"
      },
      "metadata": {
        "attachments": [
          {
            "id": 123,
            "file_type": "jpg",
            "original_name": "reference_image.jpg"
          }
        ]
      }
    }
  }' \
  http://your-domain/api/llm-manager/v1/images/generation/
```

## Implementation Details

### Enhanced Prompt Processing
When attachments are present, the system:

1. **Filters Image Files**: Only processes image files (PNG, JPEG, GIF, WebP)
2. **Vision Analysis**: Uses GPT-4o to analyze attached images with the prompt:
   ```
   "Please describe these images in detail. Focus on visual elements, style, composition, colors, and any objects or people present. Be descriptive but concise."
   ```
3. **Prompt Enhancement**: Combines the original prompt with image descriptions:
   ```
   "{original_prompt}\n\nBased on the attached images: {image_descriptions}"
   ```

### Error Handling
- If image analysis fails, the system falls back to the original prompt
- Invalid file types are ignored (only image files are processed)
- Missing files are logged but don't prevent generation

### Logging
The system logs:
- Number of images processed
- Errors during file retrieval or image analysis
- Successful prompt enhancements

## Benefits

1. **Better Context**: Reference images provide visual context for generation
2. **Style Transfer**: Users can reference specific artistic styles or compositions
3. **Iterative Design**: Build upon existing images with modifications
4. **Precise Requirements**: Show exactly what you want instead of describing it

## Use Cases

### Image Reference Use Cases
- **Style Reference**: "Create a portrait in this artistic style" + reference artwork
- **Color Palette**: "Use these colors" + reference image with desired palette
- **Composition**: "Similar layout but different subject" + reference composition
- **Object Reference**: "Include this type of object" + reference object image
- **Mood/Atmosphere**: "Same mood but different scene" + reference atmosphere

### Document Reference Use Cases
- **Design Brief**: "Create an image based on this design brief" + PDF design document
- **Product Description**: "Visualize this product" + product specification document
- **Story Illustration**: "Illustrate this scene" + text document with story excerpt
- **Technical Diagram**: "Create an artistic version of this concept" + technical documentation
- **Brand Guidelines**: "Follow these brand guidelines" + brand style guide PDF

## Limitations

- Requires GPT-4o model for file analysis (both vision and document processing)
- Falls back to original prompt if file analysis fails
- File size limits apply (as per file upload service)
- Document analysis focuses on visual concepts relevant to image generation
- Complex documents may require multiple processing attempts
