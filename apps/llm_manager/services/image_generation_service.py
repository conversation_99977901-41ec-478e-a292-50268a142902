from dxh_common.logger import Logger
from dxh_common.base.base_service import BaseService, ServiceError

from apps.llm_manager.repositories import ImageGenerationRepository, MessageRepository
from apps.llm_manager.services import AIHandlerFactory
from apps.user.services import DailyMessageUsageService
from apps.file_manager.tasks import download_generated_image_task

logger = Logger(__name__)


class ImageGenerationService(BaseService):
    def __init__(self, **kwargs):
        super().__init__(ImageGenerationRepository())
        self.ai_factory_service = AIHandlerFactory()
        self.message_usage_service = DailyMessageUsageService()
        self.message_repository = MessageRepository()
    
    def generate_image(self, user, prompt, conversation, user_message, size="1024x1024", style="natural"):
        try:
            can_send, current_count, max_limit = self.message_usage_service.check_daily_limit(user)
            if not can_send:
                return None, f"You've hit your daily message limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan!"
            
            image_handler = self.ai_factory_service.get_handler("dall-e-3")
            
            response = image_handler.generate_image(prompt, size=size, style=style)
            
            if not response:
                return None, "Failed to generate image. Please try again later."
            
            try:
                image_url = response.data[0].url
                generated_image = self.repository.create_image(
                    user=user,
                    prompt=prompt,
                    image_url=image_url,
                    size=size,
                    model="dall-e-3",
                )
                
                if conversation:
                    message_content = f"{image_url}"
                    message = self.message_repository.save_assistant_message(
                        conversation=conversation,
                        response_content=message_content,
                        user_message=user_message, 
                        model=None,
                        content_type="image"
                    )
                    
                    message.metadata = {
                        "generated_image": {
                            "id": str(generated_image.uuid),
                            "prompt": prompt,
                            "size": size,
                            "model": "dall-e-3"
                        }
                    }
                    message.save()
                
                self.message_usage_service.update_daily_usage(user)

                download_generated_image_task.delay(
                    conversation_id=conversation.uuid,
                    message_id=message.uuid,
                    user_id=user.id,
                    image_id=generated_image.id,
                    url=image_url,
                )

                return message, None
            
            except Exception as db_error:
                logger.error(f"Repository error saving generated image: {db_error}")
                return None, "Error saving the generated image. Please try again."
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return None, "An unexpected error occurred. Please try again later."
    
    def get_user_images(self, user):
        try:
            return self.repository.get_user_images(user)
        except Exception as e:
            logger.error(f"Error retrieving user images: {e}")
            raise ServiceError(f"Error retrieving user images: {e}")
