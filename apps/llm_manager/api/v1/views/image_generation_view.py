from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger
from django.utils.translation import gettext_lazy as _

from apps.llm_manager.api.v1.serializers import GeneratedImageSerializer, PromptSerializer
from apps.llm_manager.services import ImageGenerationService, ConversationService, CustomGPTService
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.user.services import DailyMessageUsageService
from apps.file_manager.services import FileService

logger = Logger(__name__)

class ImageGenerationView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.image_generation_service = ImageGenerationService()
        self.conversation_service = ConversationService()
        self.custom_gpt_service = CustomGPTService()
        self.daily_usage_service = DailyMessageUsageService()
        self.file_service = FileService()

    def post(self, request):
        try:
            payload = request.data
            serializer = PromptSerializer(data=payload)

            if not serializer.is_valid():
                return self.handle_validation_error(serializer)

            if not self.check_daily_image_generation_limit(request.user):
                return self.handle_daily_limit_exceeded(request.user)

            validated_data = serializer.validated_data
            prompt, size, style = self.extract_prompt_details(validated_data)

            # Process attachments if present
            file_paths = self.process_attachments(validated_data)

            # Enhance prompt with attachment descriptions if files are present
            if file_paths:
                logger.info(f"Processing {len(file_paths)} attachments for image generation")
                enhanced_prompt = self.enhance_prompt_with_attachments(prompt, file_paths)
                if enhanced_prompt != prompt:
                    logger.info("Prompt successfully enhanced with attachment analysis")
                    prompt = enhanced_prompt
                else:
                    logger.warning("Prompt enhancement failed, using original prompt")

            conversation, user_message, _ = self.conversation_service.create_conversation_and_message(
                request.user, validated_data, custom_gpt=None
            )

            generated_image, error = self.image_generation_service.generate_image(
                user=request.user,
                prompt=prompt,
                size=size,
                style=style,
                conversation=conversation,
                user_message=user_message
            )

            self.daily_usage_service.update_daily_image_generation_count(request.user)

            if error:
                return self.handle_image_generation_error(error)

            result = {
                "message": _("Image generated successfully"),
                "data": format_ai_response(generated_image)
            }
            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ImageGenerationView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def handle_validation_error(self, serializer):
        first_error_message = serializer.errors[next(iter(serializer.errors))][0]
        result = {
            "message": first_error_message,
            "errors": serializer.errors
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def check_daily_image_generation_limit(self, user):
        can_send, _, _ = self.daily_usage_service.check_daily_image_generation_limit(user)
        return can_send

    def handle_daily_limit_exceeded(self, user):
        current_count, max_limit = self.daily_usage_service.check_daily_image_generation_limit(user)[1:3]
        result = {
            "message": f"You've hit your daily image generation limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan!"
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def extract_prompt_details(self, validated_data):
        content = validated_data["message"]["content"]
        prompt = content["parts"][0]
        size = content.get("size") or "1024x1024"
        style = content.get("style") or "natural"
        return prompt, size, style

    def handle_image_generation_error(self, error):
        result = {
            "message": _(error)
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        """Get all images generated by the user"""
        try:
            images = self.image_generation_service.get_user_images(request.user)
            serializer = GeneratedImageSerializer(images, many=True)

            result = {
                "message": _("Images retrieved successfully"),
                "data": serializer.data
            }

            return Response(result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error({"event": "ImageGenerationView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def process_attachments(self, validated_data):
        """Process attachments from the request and return file paths"""
        file_paths = []
        user_message_data = validated_data.get("message", {})

        if user_message_data and "metadata" in user_message_data and "attachments" in user_message_data["metadata"]:
            for attachment in user_message_data["metadata"]["attachments"]:
                try:
                    file_obj = self.file_service.get(id=attachment["id"])
                    file_paths.append(file_obj.file.url)
                except Exception as e:
                    logger.error(f"Error retrieving file for image generation: {str(e)}")

        return file_paths

    def enhance_prompt_with_attachments(self, original_prompt, file_paths):
        """Enhance the prompt with descriptions of attached files (images and documents)"""
        from apps.llm_manager.services import AIHandlerFactory
        from apps.llm_manager.utils.file_types import is_vision_file_type, is_document_file_type
        import os

        try:
            # Separate files by type
            image_files = []
            document_files = []

            for file_path in file_paths:
                file_ext = os.path.splitext(file_path)[1].lower().replace('.', '')
                if is_vision_file_type(file_ext):
                    image_files.append(file_path)
                elif is_document_file_type(file_ext):
                    document_files.append(file_path)

            # If no supported files, return original prompt
            if not image_files and not document_files:
                logger.info("No supported files found for prompt enhancement")
                return original_prompt

            # Use a vision/document capable model to analyze files
            ai_factory = AIHandlerFactory()
            handler = ai_factory.get_handler("gpt-4o")  # Use a model that supports both vision and documents

            # Create a comprehensive prompt for file analysis
            analysis_prompt = [
                {
                    "role": "user",
                    "content": f"""Analyze the attached files and help enhance this image generation prompt: "{original_prompt}"

Please provide:
1. For images: Describe visual elements, style, composition, colors, objects, and artistic techniques
2. For documents: Extract key visual concepts, themes, or descriptions that could inform image generation
3. Suggest how these elements could enhance the original prompt

Focus on visual and artistic aspects that would be useful for image generation."""
                }
            ]

            # Combine all files for analysis
            all_files = image_files + document_files

            # Get file analysis
            response = handler.send_text_request_with_files(
                prompts=analysis_prompt,
                file_paths=all_files,
                stream=False
            )

            if response and hasattr(response, 'choices') and response.choices:
                file_analysis = response.choices[0].message.content

                # Enhance the original prompt with file analysis
                enhanced_prompt = f"{original_prompt}\n\nBased on the attached files: {file_analysis}"

                logger.info(f"Enhanced prompt with {len(image_files)} images and {len(document_files)} documents")
                return enhanced_prompt

        except Exception as e:
            logger.error(f"Error enhancing prompt with attachments: {str(e)}")

        # Return original prompt if enhancement fails
        return original_prompt
