from dxh_libraries.rest_framework import status, Response
from dxh_common.base.base_api_view import BaseApiView
from dxh_common.logger import Logger
from django.utils.translation import gettext_lazy as _

from apps.llm_manager.api.v1.serializers import GeneratedImageSerializer, PromptSerializer
from apps.llm_manager.services import ImageGenerationService, ConversationService, CustomGPTService
from apps.llm_manager.utils.response_formater import format_ai_response
from apps.user.services import DailyMessageUsageService

logger = Logger(__name__)

class ImageGenerationView(BaseApiView):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.image_generation_service = ImageGenerationService()
        self.conversation_service = ConversationService()
        self.custom_gpt_service = CustomGPTService()
        self.daily_usage_service = DailyMessageUsageService()

    def post(self, request):
        try:
            payload = request.data
            serializer = PromptSerializer(data=payload)

            if not serializer.is_valid():
                return self.handle_validation_error(serializer)

            if not self.check_daily_image_generation_limit(request.user):
                return self.handle_daily_limit_exceeded(request.user)

            validated_data = serializer.validated_data
            prompt, size, style = self.extract_prompt_details(validated_data)

            conversation, user_message, prompt_data = self.conversation_service.create_conversation_and_message(
                request.user, validated_data, custom_gpt=None
            )

            generated_image, error = self.image_generation_service.generate_image(
                user=request.user,
                prompt=prompt,
                size=size,
                style=style,
                conversation=conversation,
                user_message=user_message
            )

            self.daily_usage_service.update_daily_image_generation_count(request.user)

            if error:
                return self.handle_image_generation_error(error)

            result = {
                "message": _("Image generated successfully"),
                "data": format_ai_response(generated_image)
            }
            return Response(result, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error({"event": "ImageGenerationView:post", "message": "Unexpected error occurred", "error": str(e)})
            raise e

    def handle_validation_error(self, serializer):
        first_error_message = serializer.errors[next(iter(serializer.errors))][0]
        result = {
            "message": first_error_message,
            "errors": serializer.errors
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def check_daily_image_generation_limit(self, user):
        can_send, _, _ = self.daily_usage_service.check_daily_image_generation_limit(user)
        return can_send

    def handle_daily_limit_exceeded(self, user):
        current_count, max_limit = self.daily_usage_service.check_daily_image_generation_limit(user)[1:3]
        result = {
            "message": f"You've hit your daily image generation limit ({current_count}/{max_limit}). Come back tomorrow or upgrade your plan!"
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def extract_prompt_details(self, validated_data):
        content = validated_data["message"]["content"]
        prompt = content["parts"][0]
        size = content.get("size") or "1024x1024"
        style = content.get("style") or "natural"
        return prompt, size, style

    def handle_image_generation_error(self, error):
        result = {
            "message": _(error)
        }
        return Response(result, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        """Get all images generated by the user"""
        try:
            images = self.image_generation_service.get_user_images(request.user)
            serializer = GeneratedImageSerializer(images, many=True)
            
            result = {
                "message": _("Images retrieved successfully"),
                "data": serializer.data
            }
            
            return Response(result, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error({"event": "ImageGenerationView:get", "message": "Unexpected error occurred", "error": str(e)})
            raise e
